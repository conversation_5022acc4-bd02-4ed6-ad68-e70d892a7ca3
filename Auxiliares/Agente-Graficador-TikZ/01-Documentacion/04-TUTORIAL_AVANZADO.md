# 🚀 Tutorial Avanzado: Funciones Profesionales del Agente TikZ

## 🎯 **Objetivo: Dominar funciones avanzadas para resultados profesionales**

Este tutorial cubre técnicas avanzadas, personalización profunda y casos de uso complejos del Agente TikZ + Augment IA.

---

## 📋 **Prerequisitos**
- ✅ Tutorial básico completado (`03-TUTORIAL_BASICO.md`)
- ✅ Experiencia procesando al menos 3 imágenes diferentes
- ✅ Familiaridad con código TikZ básico
- ✅ VSCode configurado y funcionando

---

## 🧠 **Análisis Inteligente Avanzado con Prompts**

### **Prompts Especializados por Tipo de Gráfica**

#### **🔢 Funciones Matemáticas Complejas**
```
"Analiza esta función matemática compleja. Identifica:
- Tipo específico (polinomial, trigonométrica, exponencial, logarítmica)
- Dominio y rango exactos
- Puntos críticos (máximos, mínimos, inflexión)
- Asíntotas horizontales, verticales u oblicuas
- Comportamiento en los extremos
- Simetrías (par, impar, periódica)
Genera código TikZ con alta precisión matemática y marcas en puntos importantes."
```

#### **📐 Geometría Avanzada**
```
"Analiza esta construcción geométrica compleja. Identifica:
- Figuras principales y auxiliares
- Relaciones angulares y métricas
- Construcciones especiales (bisectrices, mediatrices, alturas)
- Círculos notables (circunscrito, inscrito, exinscrito)
- Teoremas geométricos aplicables
- Proporciones y semejanzas
Genera código TikZ con construcciones precisas y demostraciones visuales."
```

#### **📊 Diagramas Profesionales**
```
"Analiza este diagrama profesional. Identifica:
- Jerarquía y estructura organizacional
- Tipos de nodos y sus funciones
- Direccionalidad de conexiones
- Agrupaciones y categorías
- Flujo de información o procesos
- Elementos de diseño (colores, formas, estilos)
Genera código TikZ optimizado para presentaciones académicas y profesionales."
```

### **Prompts para Casos Específicos**

#### **Funciones por Partes:**
```
"Esta imagen muestra una función definida por partes. Analiza cada segmento independientemente, identifica puntos de discontinuidad, y genera código TikZ que represente claramente cada tramo con diferentes estilos."
```

#### **Sistemas de Ecuaciones:**
```
"Analiza este sistema de ecuaciones representado gráficamente. Identifica cada ecuación, puntos de intersección, regiones de solución, y genera código TikZ que destaque las soluciones del sistema."
```

#### **Transformaciones Geométricas:**
```
"Esta imagen muestra transformaciones geométricas (traslación, rotación, reflexión, homotecia). Identifica la figura original, la transformada, el centro y parámetros de transformación."
```

---

## ⚙️ **Configuración Avanzada del Agente**

### **Personalizar Configuración Principal**

#### **Editar archivo de configuración:**
```bash
cd 02-Codigo-Agente/
code 05-configuracion.json
```

#### **Parámetros clave para ajustar:**

```json
{
  "configuracion_analisis": {
    "max_iteraciones": 5,           // Más iteraciones = mejor análisis
    "umbral_similitud": 0.95,       // Precisión del análisis (0-1)
    "timeout_procesamiento": 30,    // Tiempo máximo en segundos
    "resolucion_analisis": [800, 600], // Resolución para análisis
    "umbral_deteccion": 0.7         // Sensibilidad de detección
  },
  "configuracion_tikz": {
    "escala_default": 1.2,          // Escala por defecto
    "precision_coordenadas": 2,     // Decimales en coordenadas
    "optimizacion_codigo": true,    // Optimizar código generado
    "generar_ambas_versiones": true // QTikz + LaTeX
  },
  "configuracion_augment": {
    "incluir_contexto_matematico": true,  // Contexto matemático
    "analisis_multimodal": false          // Análisis multimodal
  }
}
```

### **Configuraciones Especializadas**

#### **Para Máxima Precisión:**
```json
{
  "configuracion_analisis": {
    "max_iteraciones": 10,
    "umbral_similitud": 0.98,
    "resolucion_analisis": [1200, 900],
    "umbral_deteccion": 0.8
  },
  "configuracion_tikz": {
    "precision_coordenadas": 3,
    "optimizacion_codigo": false
  }
}
```

#### **Para Procesamiento Rápido:**
```json
{
  "configuracion_analisis": {
    "max_iteraciones": 3,
    "timeout_procesamiento": 15,
    "resolucion_analisis": [400, 300]
  },
  "configuracion_tikz": {
    "precision_coordenadas": 1,
    "optimizacion_codigo": true
  }
}
```

---

## 🎨 **Templates Avanzados y Personalización**

### **Crear Templates Personalizados**

#### **Template para Funciones Trigonométricas:**
```latex
% En 05-Templates-TikZ/06-template_trigonometrica.tikz
\begin{tikzpicture}[scale=1.2]

% Configuración específica para trigonométricas
\def\amplitud{2}
\def\periodo{2*pi}
\def\fase{0}

% Cuadrícula especializada
\draw[gray!15, thin] (-2*pi,-3) grid[step=pi/4] (2*pi,3);

% Ejes con marcas en múltiplos de π
\draw[thick, ->] (-2*pi,0) -- (2*pi,0) node[below right] {$x$};
\draw[thick, ->] (0,-3) -- (0,3) node[above left] {$y$};

% Marcas especiales en π
\foreach \x in {-2,-1,1,2} {
  \draw (\x*pi,-0.1) -- (\x*pi,0.1) node[below] {$\x\pi$};
}

% Función trigonométrica
\draw[blue, very thick] plot[domain=-2*pi:2*pi, samples=100] 
  (\x, {\amplitud*sin(deg(\x + \fase))});

% Líneas de amplitud
\draw[dashed, red] (-2*pi,\amplitud) -- (2*pi,\amplitud) node[right] {$A = \amplitud$};
\draw[dashed, red] (-2*pi,-\amplitud) -- (2*pi,-\amplitud) node[right] {$-A = -\amplitud$};

\end{tikzpicture}
```

#### **Template para Geometría Analítica:**
```latex
% En 05-Templates-TikZ/07-template_geometria_analitica.tikz
\begin{tikzpicture}[scale=1.2]

% Sistema coordenado con origen centrado
\draw[gray!20, thin] (-5,-4) grid[step=0.5] (5,4);
\draw[thick, ->] (-5,0) -- (5,0) node[below right] {$x$};
\draw[thick, ->] (0,-4) -- (0,4) node[above left] {$y$};

% Puntos importantes
\coordinate (A) at (-2,1);
\coordinate (B) at (3,2);
\coordinate (C) at (1,-2);

% Figura geométrica
\draw[blue, thick] (A) -- (B) -- (C) -- cycle;

% Coordenadas de puntos
\fill[red] (A) circle (2pt) node[above left] {$A(-2,1)$};
\fill[red] (B) circle (2pt) node[above right] {$B(3,2)$};
\fill[red] (C) circle (2pt) node[below] {$C(1,-2)$};

% Cálculos automáticos
\draw[dashed, green] (A) -- ($(B)!(A)!(C)$) node[midway, left] {$h$};

% Medidas calculadas
\node[below left] at (-3,-3) {
  \begin{tabular}{l}
    $|AB| = \sqrt{(3-(-2))^2 + (2-1)^2} = \sqrt{26}$ \\
    $|BC| = \sqrt{(1-3)^2 + (-2-2)^2} = \sqrt{20}$ \\
    $|CA| = \sqrt{(-2-1)^2 + (1-(-2))^2} = \sqrt{18}$
  \end{tabular}
};

\end{tikzpicture}
```

### **Usar Templates Personalizados**

#### **Configurar template específico:**
```json
// En 05-configuracion.json
{
  "configuracion_templates": {
    "template_trigonometrica": "05-Templates-TikZ/06-template_trigonometrica.tikz",
    "template_geometria_analitica": "05-Templates-TikZ/07-template_geometria_analitica.tikz"
  }
}
```

---

## 🔄 **Flujo de Trabajo Avanzado**

### **Procesamiento en Lote**

#### **Script para múltiples imágenes:**
```python
# En 04-Ejemplos-y-Pruebas/07-procesamiento_lote.py
import os
import subprocess
from pathlib import Path

def procesar_lote(directorio_imagenes, prompt_personalizado=None):
    """Procesar múltiples imágenes automáticamente."""
    
    agente_path = Path("../02-Codigo-Agente/01-agente_principal.py")
    imagenes = list(Path(directorio_imagenes).glob("*.png")) + \
               list(Path(directorio_imagenes).glob("*.jpg"))
    
    for imagen in imagenes:
        print(f"🔄 Procesando: {imagen.name}")
        
        cmd = ["python3", str(agente_path), str(imagen)]
        if prompt_personalizado:
            cmd.append(prompt_personalizado)
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                print(f"✅ {imagen.name} procesado exitosamente")
            else:
                print(f"❌ Error procesando {imagen.name}: {result.stderr}")
        except subprocess.TimeoutExpired:
            print(f"⏱️ Timeout procesando {imagen.name}")

# Uso
if __name__ == "__main__":
    procesar_lote("mis_imagenes/", "Analiza con máxima precisión matemática")
```

### **Análisis Comparativo**

#### **Comparar diferentes configuraciones:**
```python
# En 04-Ejemplos-y-Pruebas/08-analisis_comparativo.py
import json
import shutil
from pathlib import Path

def comparar_configuraciones(imagen, configuraciones):
    """Comparar resultados con diferentes configuraciones."""
    
    resultados = {}
    config_original = "02-Codigo-Agente/05-configuracion.json"
    
    # Backup configuración original
    shutil.copy(config_original, f"{config_original}.backup")
    
    for nombre, config in configuraciones.items():
        print(f"🔄 Probando configuración: {nombre}")
        
        # Aplicar configuración
        with open(config_original, 'w') as f:
            json.dump(config, f, indent=2)
        
        # Procesar imagen
        # ... código de procesamiento ...
        
        # Guardar resultado
        resultados[nombre] = {
            "tiempo": tiempo_procesamiento,
            "calidad": evaluar_calidad(),
            "archivo_generado": f"tikz_generado/{imagen.stem}_{nombre}.tikz"
        }
    
    # Restaurar configuración original
    shutil.move(f"{config_original}.backup", config_original)
    
    return resultados
```

---

## 🎯 **Casos de Uso Avanzados**

### **Funciones Matemáticas Complejas**

#### **Funciones Implícitas:**
```
Prompt: "Esta imagen muestra una curva definida implícitamente. Analiza la ecuación implícita, identifica características especiales (bucles, cúspides, asíntotas) y genera código TikZ usando parametrización o métodos numéricos."
```

#### **Campos Vectoriales:**
```
Prompt: "Analiza este campo vectorial. Identifica la dirección y magnitud de los vectores en diferentes puntos, puntos críticos, y genera código TikZ con flechas proporcionales."
```

### **Geometría Avanzada**

#### **Geometría Proyectiva:**
```
Prompt: "Esta imagen muestra elementos de geometría proyectiva. Identifica puntos al infinito, líneas de fuga, razones cruzadas, y genera código TikZ con perspectiva correcta."
```

#### **Geometría Diferencial:**
```
Prompt: "Analiza esta curva desde perspectiva de geometría diferencial. Identifica curvatura, torsión, puntos de inflexión, y genera código TikZ con círculos osculadores."
```

### **Diagramas Especializados**

#### **Diagramas de Feynman:**
```
Prompt: "Este es un diagrama de Feynman. Identifica partículas, interacciones, vértices, líneas de tiempo, y genera código TikZ con convenciones estándar de física de partículas."
```

#### **Diagramas de Circuitos:**
```
Prompt: "Analiza este circuito eléctrico. Identifica componentes (resistencias, capacitores, inductores), conexiones, nodos, y genera código TikZ con símbolos estándar IEEE."
```

---

## 🔧 **Optimización y Debugging**

### **Análisis de Rendimiento**

#### **Medir tiempos de procesamiento:**
```python
import time
import psutil

def benchmark_agente(imagen, configuracion):
    """Benchmark del rendimiento del agente."""
    
    inicio = time.time()
    memoria_inicial = psutil.Process().memory_info().rss
    
    # Procesar imagen
    resultado = procesar_imagen(imagen, configuracion)
    
    fin = time.time()
    memoria_final = psutil.Process().memory_info().rss
    
    return {
        "tiempo_total": fin - inicio,
        "memoria_usada": memoria_final - memoria_inicial,
        "calidad_resultado": evaluar_calidad(resultado),
        "lineas_codigo": contar_lineas(resultado["archivo_tikz"])
    }
```

### **Debugging Avanzado**

#### **Logs detallados:**
```json
// En 05-configuracion.json
{
  "configuracion_logging": {
    "level": "DEBUG",
    "archivo_log": "logs/agente_debug_{timestamp}.log",
    "mostrar_progreso_detallado": true,
    "guardar_archivos_temporales": true
  }
}
```

#### **Análisis de errores:**
```python
def analizar_errores(log_file):
    """Analizar patrones de errores en logs."""
    
    with open(log_file, 'r') as f:
        logs = f.read()
    
    errores = {
        "timeout": logs.count("TimeoutError"),
        "memoria": logs.count("MemoryError"),
        "opencv": logs.count("cv2.error"),
        "augment": logs.count("AugmentError")
    }
    
    return errores
```

---

## 📊 **Métricas de Calidad**

### **Evaluación Automática**

#### **Métricas de similitud visual:**
```python
def calcular_similitud_visual(imagen_original, codigo_tikz):
    """Calcular similitud entre imagen original y TikZ renderizado."""
    
    # Renderizar TikZ a imagen
    imagen_tikz = renderizar_tikz(codigo_tikz)
    
    # Calcular métricas
    ssim = calcular_ssim(imagen_original, imagen_tikz)
    histograma = comparar_histogramas(imagen_original, imagen_tikz)
    contornos = comparar_contornos(imagen_original, imagen_tikz)
    
    return {
        "ssim": ssim,
        "histograma": histograma,
        "contornos": contornos,
        "puntuacion_total": (ssim + histograma + contornos) / 3
    }
```

### **Métricas de Código**

#### **Calidad del código TikZ:**
```python
def evaluar_calidad_codigo(codigo_tikz):
    """Evaluar calidad del código TikZ generado."""
    
    metricas = {
        "complejidad": contar_comandos_tikz(codigo_tikz),
        "legibilidad": evaluar_comentarios(codigo_tikz),
        "optimizacion": detectar_redundancias(codigo_tikz),
        "estandares": verificar_convenciones(codigo_tikz)
    }
    
    return metricas
```

---

## 🎯 **Próximos Pasos Avanzados**

### **Después de dominar funciones avanzadas:**
```
1. 📖 Estudiar: 08-PERSONALIZACION.md (personalización profunda)
2. 📖 Contribuir: 09-MEJORAS_FUTURAS.md (desarrollo del proyecto)
3. 🔬 Experimentar: Crear tus propios templates especializados
4. 🎨 Integrar: Combinar con otras herramientas de tu flujo de trabajo
```

### **Para convertirte en experto:**
```
1. Dominar configuración avanzada para casos específicos
2. Crear biblioteca personal de templates especializados
3. Desarrollar scripts de automatización personalizados
4. Contribuir mejoras al proyecto principal
```

---

## 🎉 **¡Nivel Avanzado Completado!**

**Has dominado las funciones avanzadas del Agente TikZ + Augment IA.**

### **Habilidades adquiridas:**
- ✅ **Prompts especializados** para análisis preciso
- ✅ **Configuración avanzada** para casos específicos
- ✅ **Templates personalizados** para necesidades únicas
- ✅ **Flujo de trabajo optimizado** para productividad máxima
- ✅ **Debugging y optimización** para resultados perfectos

### **Siguiente nivel:** 📖 **`08-PERSONALIZACION.md`**

**¡Ahora puedes manejar cualquier caso de uso matemático complejo!** 🚀✨

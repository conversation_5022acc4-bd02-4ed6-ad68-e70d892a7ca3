# ⚙️ Referencia Completa: Integración VSCode

## 🎯 **Guía Completa de VSCode para Uso Diario del Agente TikZ**

Esta referencia cubre toda la integración del Agente TikZ con VSCode, desde configuración hasta uso avanzado diario.

---

## 📋 **Tasks Disponibles**

### **🎨 Agente TikZ + Augment: <PERSON><PERSON><PERSON>**
```
Función: Análisis automático estándar
Input: Imagen seleccionada en VSCode
Output: Ambas versiones TikZ + metadatos
Uso: Procesamiento rápido y estándar
Tiempo: 15-30 segundos
```

### **🎨 Agente TikZ: Análisis Personalizado**
```
Función: Análisis con prompt específico
Input: Imagen + prompt personalizado del usuario
Output: TikZ optimizado según prompt
Uso: Casos especiales o requerimientos específicos
Tiempo: 20-40 segundos
```

### **📋 Compilar TikZ con LaTeX**
```
Función: Compilar archivo TikZ a PDF
Input: Archivo .tikz seleccionado
Output: PDF compilado
Uso: Verificar resultado final
Tiempo: 5-10 segundos
```

### **🔍 Ver Resultado TikZ**
```
Función: Listar archivos generados
Output: Lista de archivos en tikz_generado/
Uso: Exploración rápida de resultados
Tiempo: Instantáneo
```

### **🧪 Test Agente Completo**
```
Función: Ejecutar suite completa de tests
Output: Verificación de funcionamiento
Uso: Debugging y verificación
Tiempo: 1-2 minutos
```

---

## ⌨️ **Atajos de Teclado**

### **Atajos Principales**
```
Ctrl+Shift+P        → Command Palette (acceso a tasks)
Ctrl+`              → Terminal integrado
Ctrl+Shift+E        → Explorer (navegación de archivos)
Ctrl+O              → Abrir archivo (para seleccionar imagen)
F1                  → Command Palette alternativo
```

### **Atajos Específicos del Agente**
```
Ctrl+Shift+P → "Tasks: Run Task"                    → Acceso a todos los tasks
Ctrl+Shift+P → "Tasks: Rerun Last Task"            → Repetir último task
Ctrl+Shift+P → "Developer: Reload Window"          → Recargar configuración
```

### **Configurar Atajos Personalizados**

#### **Editar keybindings.json:**
```json
// En .vscode/keybindings.json o 03-Configuracion-VSCode/04-keybindings.json
[
  {
    "key": "ctrl+alt+t",
    "command": "workbench.action.tasks.runTask",
    "args": "🎨 Agente TikZ + Augment: Analizar Imagen"
  },
  {
    "key": "ctrl+alt+shift+t",
    "command": "workbench.action.tasks.runTask",
    "args": "🎨 Agente TikZ: Análisis Personalizado"
  },
  {
    "key": "ctrl+alt+c",
    "command": "workbench.action.tasks.runTask",
    "args": "📋 Compilar TikZ con LaTeX"
  }
]
```

---

## 🎨 **Snippets TikZ Incluidos**

### **Snippets Básicos**

#### **tikz-agente-funcion + Tab**
```latex
% Generado por Agente TikZ + Augment
% Tipo: Función matemática
\begin{tikzpicture}[scale=1.2]

% Cuadrícula de fondo sutil
\draw[gray!20, thin] (-4,-3) grid[step=0.5] (4,3);

% Ejes coordenados principales
\draw[thick, ->] (-4,0) -- (4,0) node[below right] {$x$};
\draw[thick, ->] (0,-3) -- (0,3) node[above left] {$y$};

% Función principal
\draw[blue, very thick] plot[domain=-3:3, samples=50] (\x, {función});

% Puntos importantes
\fill[red] (x,y) circle (3pt) node[below right] {Etiqueta};

\end{tikzpicture}
```

#### **tikz-agente-geometria + Tab**
```latex
% Generado por Agente TikZ + Augment
% Tipo: Figura geométrica
\usetikzlibrary{calc, angles, quotes}
\begin{tikzpicture}[scale=1.2]

% Puntos principales
\coordinate (A) at (0,0);
\coordinate (B) at (3,0);
\coordinate (C) at (1.5,2.6);

% Figura geométrica
\draw[blue, thick, fill=blue!20] (A) -- (B) -- (C) -- cycle;

% Etiquetas de vértices
\fill[black] (A) circle (2pt) node[below left] {$A$};
\fill[black] (B) circle (2pt) node[below right] {$B$};
\fill[black] (C) circle (2pt) node[above] {$C$};

\end{tikzpicture}
```

#### **tikz-agente-ejes + Tab**
```latex
% Generado por Agente TikZ + Augment
% Tipo: Ejes coordenados
\begin{tikzpicture}[scale=1.2]

% Cuadrícula de fondo (opcional)
\draw[gray!20, thin] (-4,-3) grid[step=0.5] (4,3);

% Ejes principales con flechas
\draw[thick, ->] (-4,0) -- (4,0) node[below right] {$x$};
\draw[thick, ->] (0,-3) -- (0,3) node[above left] {$y$};

% Marcas en ejes
\foreach \x in {-3,-2,-1,1,2,3}
  \draw (\x,-0.1) -- (\x,0.1) node[below] {\x};
\foreach \y in {-2,-1,1,2}
  \draw (-0.1,\y) -- (0.1,\y) node[left] {\y};

% Origen marcado
\fill[black] (0,0) circle (1.5pt);

\end{tikzpicture}
```

### **Snippets Avanzados**

#### **tikz-agente-completo + Tab**
```latex
% ==============================
% CÓDIGO TIKZ GENERADO POR AGENTE + AUGMENT IA
% ==============================
% Imagen analizada: imagen.png
% Tipo detectado: función_matemática
% Timestamp: 2025-01-13

\documentclass{standalone}
\usepackage{tikz}
\usepackage{pgfplots}
\usetikzlibrary{calc,arrows.meta,positioning}
\pgfplotsset{compat=1.18}

\begin{document}
\begin{tikzpicture}[scale=1.2]

% Contenido principal aquí

\end{tikzpicture}
\end{document}
```

---

## 🔧 **Configuración VSCode**

### **Settings.json Optimizado**

#### **Configuración recomendada:**
```json
// En .vscode/settings.json
{
  // Configuración LaTeX Workshop
  "latex-workshop.latex.autoBuild.run": "onSave",
  "latex-workshop.latex.tools": [
    {
      "name": "pdflatex",
      "command": "pdflatex",
      "args": [
        "-synctex=1",
        "-interaction=nonstopmode",
        "-file-line-error",
        "%DOC%"
      ]
    }
  ],
  
  // Configuración Python
  "python.defaultInterpreterPath": "python3",
  "python.terminal.activateEnvironment": true,
  
  // Configuración archivos
  "files.associations": {
    "*.tikz": "latex",
    "*.Rnw": "latex"
  },
  
  // Configuración terminal
  "terminal.integrated.defaultProfile.linux": "bash",
  "terminal.integrated.cwd": "${workspaceFolder}",
  
  // Configuración explorador
  "explorer.confirmDelete": false,
  "explorer.confirmDragAndDrop": false,
  
  // Configuración específica del agente
  "agente-tikz.autoSave": true,
  "agente-tikz.showProgress": true,
  "agente-tikz.defaultPrompt": "Analiza con alta precisión matemática"
}
```

### **Extensiones Recomendadas**

#### **Esenciales:**
```
- LaTeX Workshop (James-Yu.latex-workshop)
- Python (ms-python.python)
- TikZ (tecosaur.latex-utilities)
```

#### **Útiles:**
```
- GitLens (eamodio.gitlens)
- Bracket Pair Colorizer (CoenraadS.bracket-pair-colorizer)
- Auto Rename Tag (formulahendry.auto-rename-tag)
- Path Intellisense (christian-kohler.path-intellisense)
```

#### **Instalar extensiones automáticamente:**
```json
// En .vscode/extensions.json
{
  "recommendations": [
    "james-yu.latex-workshop",
    "ms-python.python",
    "tecosaur.latex-utilities",
    "eamodio.gitlens"
  ]
}
```

---

## 🔄 **Flujo de Trabajo Diario**

### **Flujo Estándar (5 minutos por imagen)**

#### **1. Preparación (30 segundos)**
```
1. Abrir VSCode en directorio del proyecto
2. Verificar que tikz_generado/ existe
3. Tener imagen lista para procesar
```

#### **2. Procesamiento (30 segundos)**
```
1. File → Open → Seleccionar imagen
2. Ctrl+Shift+P → "Tasks: Run Task"
3. Seleccionar: "🎨 Agente TikZ + Augment: Analizar Imagen"
4. Esperar procesamiento (15-30 segundos)
```

#### **3. Verificación (1 minuto)**
```
1. Revisar terminal para confirmación de éxito
2. Abrir tikz_generado/ en Explorer
3. Verificar archivos generados:
   - nombre_qtikz.tikz
   - nombre_agente.tikz
   - nombre_analisis.json
```

#### **4. Testing (2 minutos)**
```
1. Abrir QTikz externamente
2. Cargar archivo *_qtikz.tikz
3. Verificar renderizado correcto
4. Hacer ajustes si es necesario
```

#### **5. Uso Final (1 minuto)**
```
1. Copiar código de *_agente.tikz
2. Incluir en documento LaTeX
3. O usar snippets para modificaciones
```

### **Flujo Avanzado (Análisis Personalizado)**

#### **Para casos específicos:**
```
1. Analizar imagen manualmente primero
2. Formular prompt específico
3. Usar task "Análisis Personalizado"
4. Ingresar prompt detallado
5. Comparar con resultado estándar
6. Iterar si es necesario
```

#### **Ejemplos de prompts por contexto:**

**Para exámenes ICFES:**
```
"Analiza esta función matemática para examen ICFES. Identifica elementos clave que estudiantes deben reconocer: dominio, rango, intersecciones, comportamiento. Genera código TikZ claro y educativo."
```

**Para publicaciones académicas:**
```
"Analiza con máxima precisión matemática. Identifica todos los elementos técnicos, propiedades matemáticas y relaciones. Genera código TikZ profesional para publicación académica."
```

**Para presentaciones:**
```
"Analiza para presentación educativa. Enfócate en claridad visual, elementos destacados y facilidad de comprensión. Genera código TikZ optimizado para proyección."
```

---

## 🛠️ **Debugging en VSCode**

### **Problemas Comunes y Soluciones**

#### **❌ Tasks no aparecen**
```
Diagnóstico:
1. Verificar: ls .vscode/tasks.json
2. Verificar sintaxis: cat .vscode/tasks.json | python3 -m json.tool

Solución:
1. Ctrl+Shift+P → "Developer: Reload Window"
2. Si persiste: reconfigurar con 05-setup_vscode.py
```

#### **❌ Terminal no ejecuta Python**
```
Diagnóstico:
1. Verificar: which python3
2. Verificar PATH: echo $PATH

Solución:
1. Configurar intérprete: Ctrl+Shift+P → "Python: Select Interpreter"
2. O editar settings.json: "python.defaultInterpreterPath"
```

#### **❌ Archivos no se generan**
```
Diagnóstico:
1. Verificar permisos: ls -la tikz_generado/
2. Verificar espacio: df -h
3. Revisar logs en terminal

Solución:
1. Crear directorio: mkdir -p tikz_generado
2. Ajustar permisos: chmod 755 tikz_generado
3. Verificar imagen válida: file imagen.png
```

### **Logs y Debugging**

#### **Ver logs detallados:**
```
1. Abrir terminal integrado: Ctrl+`
2. Ejecutar manualmente:
   cd Auxiliares/Agente-Graficador-TikZ/02-Codigo-Agente/
   python3 01-agente_principal.py imagen.png 2>&1 | tee debug.log
3. Revisar debug.log para errores específicos
```

#### **Configurar logging avanzado:**
```json
// En 02-Codigo-Agente/05-configuracion.json
{
  "configuracion_logging": {
    "level": "DEBUG",
    "mostrar_progreso_detallado": true,
    "archivo_log": "logs/agente_vscode_{timestamp}.log"
  }
}
```

---

## 📊 **Monitoreo y Métricas**

### **Panel de Output**

#### **Ver progreso en tiempo real:**
```
1. View → Output (Ctrl+Shift+U)
2. Seleccionar: "Tasks" en dropdown
3. Monitorear progreso del agente
```

#### **Información mostrada:**
```
🔍 Analizando imagen: imagen.png
🧠 Ejecutando análisis Augment IA...
🎨 Generando código TikZ...
📄 Guardando archivos...
✅ PROCESAMIENTO COMPLETADO
📊 Tiempo total: 23.5 segundos
📈 Confianza del análisis: 0.94
📝 Líneas de código generadas: 42
```

### **Estadísticas de Uso**

#### **Tracking automático:**
```json
// En logs/estadisticas_uso.json (generado automáticamente)
{
  "imagenes_procesadas": 15,
  "tiempo_promedio": 28.3,
  "tipos_detectados": {
    "funcion_matematica": 8,
    "figura_geometrica": 4,
    "diagrama": 3
  },
  "confianza_promedio": 0.89,
  "errores_total": 2
}
```

---

## 🎯 **Optimización del Flujo**

### **Configuración para Máxima Productividad**

#### **Workspace personalizado:**
```json
// En proyecto.code-workspace
{
  "folders": [
    {
      "path": "."
    }
  ],
  "settings": {
    "agente-tikz.autoOpen": true,
    "agente-tikz.autoCompile": false,
    "agente-tikz.showNotifications": true
  },
  "tasks": {
    "version": "2.0.0",
    "tasks": [
      // Tasks del agente incluidos automáticamente
    ]
  }
}
```

#### **Configuración de proyecto:**
```json
// En .vscode/settings.json (específico del proyecto)
{
  "files.exclude": {
    "**/.git": true,
    "**/logs": false,
    "**/tikz_generado": false
  },
  "search.exclude": {
    "**/logs": true
  }
}
```

### **Automatización Avanzada**

#### **Watchers automáticos:**
```json
// En .vscode/tasks.json (agregar task)
{
  "label": "🔄 Watch Images",
  "type": "shell",
  "command": "python3",
  "args": [
    "Auxiliares/Agente-Graficador-TikZ/04-Ejemplos-y-Pruebas/09-watcher.py"
  ],
  "group": "build",
  "presentation": {
    "echo": true,
    "reveal": "always",
    "focus": false,
    "panel": "shared"
  },
  "runOptions": {
    "runOn": "folderOpen"
  }
}
```

---

## 🎉 **Maestría en VSCode Completada**

**Has dominado completamente la integración del Agente TikZ con VSCode.**

### **Habilidades adquiridas:**
- ✅ **Tasks optimizados** para flujo diario eficiente
- ✅ **Atajos personalizados** para máxima velocidad
- ✅ **Snippets especializados** para edición rápida
- ✅ **Debugging avanzado** para solución de problemas
- ✅ **Configuración profesional** para productividad máxima

### **Siguiente paso:** 📖 **`06-SOLUCION_QTIKZ.md`**

**¡Ahora puedes usar el agente con máxima eficiencia en VSCode!** ⚙️✨

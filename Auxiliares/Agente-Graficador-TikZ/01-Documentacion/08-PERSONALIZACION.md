# ⚙️ Personalización Avanzada del Agente TikZ

## 🎯 **Configuración Profunda para Casos Específicos**

Esta guía te enseña a personalizar completamente el Agente TikZ para adaptarlo a tus necesidades específicas y flujo de trabajo.

---

## 🔧 **Configuración Principal del Agente**

### **Archivo de Configuración Central**

#### **Ubicación:** `02-Codigo-Agente/05-configuracion.json`

#### **Estructura completa:**
```json
{
  "agente_info": {
    "nombre": "Agente Graficador TikZ + Augment",
    "version": "1.0.0",
    "autor": "Tu Nombre",
    "descripcion": "Personalizado para [tu caso de uso]"
  },
  
  "configuracion_analisis": {
    "max_iteraciones": 5,
    "umbral_similitud": 0.95,
    "timeout_procesamiento": 30,
    "resolucion_analisis": [800, 600],
    "tipos_imagen_soportados": ["png", "jpg", "jpeg", "bmp", "tiff"],
    "umbral_deteccion": 0.7
  },
  
  "configuracion_tikz": {
    "escala_default": 1.2,
    "precision_coordenadas": 2,
    "optimizacion_codigo": true,
    "validacion_automatica": true,
    "generar_ambas_versiones": true,
    "compilacion_latex": true
  },
  
  "configuracion_colores": {
    "colores_default": ["blue", "red", "green", "orange", "purple"],
    "mapeo_colores_tikz": {
      "azul": "blue",
      "rojo": "red",
      "verde": "green"
    },
    "intensidades": ["!20", "!40", "!60", "!80", "!100"]
  },
  
  "configuracion_augment": {
    "prompt_base_funciones": "Tu prompt personalizado para funciones",
    "prompt_base_geometria": "Tu prompt personalizado para geometría",
    "incluir_contexto_matematico": true,
    "analisis_multimodal": false
  }
}
```

---

## 🎨 **Personalización por Tipo de Uso**

### **Para Educación (ICFES/Exámenes)**

#### **Configuración optimizada:**
```json
{
  "configuracion_educacion": {
    "enfoque": "claridad_pedagogica",
    "colores_educativos": ["blue", "red", "green", "black"],
    "escala_proyeccion": 1.5,
    "elementos_destacados": true,
    "simplificacion_automatica": true
  },
  
  "configuracion_tikz": {
    "escala_default": 1.5,
    "precision_coordenadas": 1,
    "grosor_lineas": "thick",
    "tamaño_puntos": "4pt",
    "fuente_etiquetas": "\\large"
  },
  
  "configuracion_augment": {
    "prompt_base_funciones": "Analiza esta función matemática para estudiantes de bachillerato. Identifica elementos clave que deben reconocer: dominio, rango, intersecciones, comportamiento. Genera código TikZ claro y educativo con colores contrastados.",
    "prompt_base_geometria": "Analiza esta figura geométrica para enseñanza. Identifica propiedades fundamentales, medidas importantes y relaciones. Genera código TikZ pedagógico con construcciones claras.",
    "contexto_educativo": "bachillerato_colombia"
  }
}
```

### **Para Investigación Académica**

#### **Configuración para papers:**
```json
{
  "configuracion_academica": {
    "enfoque": "precision_maxima",
    "estandar_publicacion": "ieee",
    "colores_monocromaticos": true,
    "grosor_publicacion": true
  },
  
  "configuracion_tikz": {
    "escala_default": 1.0,
    "precision_coordenadas": 3,
    "optimizacion_codigo": false,
    "comentarios_detallados": true,
    "compilacion_standalone": true
  },
  
  "configuracion_augment": {
    "prompt_base_funciones": "Analiza con máxima precisión matemática para publicación académica. Identifica todas las propiedades técnicas, comportamiento asintótico, puntos críticos y características analíticas. Genera código TikZ profesional.",
    "incluir_contexto_matematico": true,
    "precision_analisis": "maxima",
    "terminologia_tecnica": true
  }
}
```

### **Para Presentaciones**

#### **Configuración para slides:**
```json
{
  "configuracion_presentacion": {
    "enfoque": "impacto_visual",
    "colores_vibrantes": true,
    "elementos_grandes": true,
    "animacion_compatible": true
  },
  
  "configuracion_tikz": {
    "escala_default": 2.0,
    "grosor_lineas": "very thick",
    "colores_intensos": ["blue!80", "red!80", "green!80"],
    "tamaño_fuente": "\\Large",
    "sombras": true
  },
  
  "configuracion_augment": {
    "prompt_base": "Analiza para presentación visual impactante. Enfócate en elementos principales, claridad a distancia y impacto visual. Genera código TikZ optimizado para proyección con colores vibrantes."
  }
}
```

---

## 🔧 **Personalización de Prompts Augment**

### **Prompts Especializados por Materia**

#### **Cálculo Diferencial:**
```json
{
  "prompts_calculo": {
    "derivadas": "Analiza esta gráfica de función y su derivada. Identifica puntos críticos, máximos, mínimos, puntos de inflexión y comportamiento de la derivada. Genera código TikZ que muestre claramente la relación entre f(x) y f'(x).",
    
    "integrales": "Analiza esta región de integración. Identifica límites de integración, función integrando y área bajo la curva. Genera código TikZ con sombreado de área y marcas de límites.",
    
    "limites": "Analiza este comportamiento límite. Identifica asíntotas, discontinuidades y comportamiento en el infinito. Genera código TikZ que ilustre claramente el concepto de límite."
  }
}
```

#### **Geometría Analítica:**
```json
{
  "prompts_geometria_analitica": {
    "conicas": "Analiza esta sección cónica. Identifica tipo (parábola, elipse, hipérbola), centro, focos, vértices y ecuación. Genera código TikZ con elementos geométricos precisos y construcciones auxiliares.",
    
    "vectores": "Analiza estos vectores en el plano. Identifica componentes, magnitud, dirección y operaciones vectoriales. Genera código TikZ con vectores proporcionales y etiquetas claras.",
    
    "transformaciones": "Analiza esta transformación geométrica. Identifica tipo de transformación, centro, parámetros y figura resultante. Genera código TikZ que muestre claramente la transformación."
  }
}
```

#### **Estadística:**
```json
{
  "prompts_estadistica": {
    "distribuciones": "Analiza esta distribución de probabilidad. Identifica tipo de distribución, parámetros, media, varianza y características. Genera código TikZ con curva suave y áreas sombreadas.",
    
    "datos": "Analiza estos datos estadísticos. Identifica tipo de gráfica (histograma, boxplot, dispersión), tendencias y valores atípicos. Genera código TikZ con representación clara de los datos."
  }
}
```

### **Configurar Prompts Personalizados**

#### **Editar configuración:**
```json
{
  "configuracion_augment": {
    "prompts_personalizados": {
      "mi_caso_especifico": "Tu prompt personalizado aquí...",
      "otro_caso": "Otro prompt específico..."
    },
    
    "mapeo_automatico": {
      "palabras_clave": {
        "derivada": "prompts_calculo.derivadas",
        "integral": "prompts_calculo.integrales",
        "vector": "prompts_geometria_analitica.vectores"
      }
    }
  }
}
```

---

## 🎨 **Personalización Visual**

### **Esquemas de Colores Personalizados**

#### **Crear paleta personalizada:**
```json
{
  "configuracion_colores": {
    "paletas_personalizadas": {
      "paleta_institucional": {
        "primario": "blue!70",
        "secundario": "red!60",
        "acento": "green!50",
        "neutro": "gray!30"
      },
      
      "paleta_accesible": {
        "alto_contraste": ["black", "white", "red!80", "blue!80"],
        "daltonismo_friendly": ["blue", "orange", "green!70", "purple!70"]
      },
      
      "paleta_monocromatica": {
        "escala_grises": ["black", "gray!80", "gray!60", "gray!40", "gray!20"]
      }
    }
  }
}
```

#### **Aplicar paleta específica:**
```python
# En 02-Codigo-Agente/01-agente_principal.py
def aplicar_paleta_colores(codigo_tikz, paleta="institucional"):
    """Aplicar paleta de colores específica al código TikZ."""
    
    paletas = {
        "institucional": {
            "blue": "blue!70",
            "red": "red!60",
            "green": "green!50"
        },
        "accesible": {
            "blue": "blue!80",
            "red": "orange!80",
            "green": "green!70"
        }
    }
    
    if paleta in paletas:
        for color_original, color_nuevo in paletas[paleta].items():
            codigo_tikz = codigo_tikz.replace(color_original, color_nuevo)
    
    return codigo_tikz
```

### **Estilos Personalizados**

#### **Definir estilos TikZ:**
```latex
% En templates o código generado
\tikzset{
  % Estilo para educación
  estilo_educativo/.style={
    thick,
    font=\large,
    every node/.style={font=\large}
  },
  
  % Estilo para investigación
  estilo_academico/.style={
    thin,
    font=\footnotesize,
    precision=high
  },
  
  % Estilo para presentación
  estilo_presentacion/.style={
    very thick,
    font=\Large,
    every node/.style={font=\Large, text=white, fill=black!80}
  }
}
```

---

## 🔄 **Flujos de Trabajo Personalizados**

### **Automatización Específica**

#### **Script para lote educativo:**
```python
# En 04-Ejemplos-y-Pruebas/12-flujo_educativo.py
import json
import subprocess
from pathlib import Path

def procesar_lote_educativo(directorio_imagenes):
    """Procesar imágenes con configuración educativa."""
    
    # Configuración específica para educación
    config_educativa = {
        "configuracion_tikz": {
            "escala_default": 1.5,
            "precision_coordenadas": 1,
            "colores_educativos": True
        },
        "configuracion_augment": {
            "prompt_base": "Analiza para estudiantes de bachillerato..."
        }
    }
    
    # Aplicar configuración temporal
    config_path = "02-Codigo-Agente/05-configuracion.json"
    backup_config(config_path)
    apply_config(config_path, config_educativa)
    
    try:
        # Procesar todas las imágenes
        for imagen in Path(directorio_imagenes).glob("*.png"):
            procesar_imagen_educativa(imagen)
    finally:
        # Restaurar configuración original
        restore_config(config_path)

def procesar_imagen_educativa(imagen):
    """Procesar imagen individual con post-procesamiento educativo."""
    
    # Procesar con agente
    resultado = subprocess.run([
        "python3", "02-Codigo-Agente/01-agente_principal.py", 
        str(imagen), 
        "Analiza para estudiantes con énfasis en claridad pedagógica"
    ])
    
    # Post-procesamiento educativo
    if resultado.returncode == 0:
        optimizar_para_educacion(imagen.stem)

def optimizar_para_educacion(nombre_base):
    """Optimizar código TikZ para uso educativo."""
    
    archivo_tikz = f"tikz_generado/{nombre_base}_agente.tikz"
    
    with open(archivo_tikz, 'r') as f:
        codigo = f.read()
    
    # Aplicar optimizaciones educativas
    codigo = aumentar_grosor_lineas(codigo)
    codigo = simplificar_etiquetas(codigo)
    codigo = aplicar_colores_educativos(codigo)
    
    # Guardar versión educativa
    with open(f"tikz_generado/{nombre_base}_educativo.tikz", 'w') as f:
        f.write(codigo)
```

### **Integración con Herramientas Externas**

#### **Exportación automática:**
```python
# En 04-Ejemplos-y-Pruebas/13-exportacion_automatica.py
def exportar_multiples_formatos(archivo_tikz):
    """Exportar TikZ a múltiples formatos automáticamente."""
    
    formatos = {
        "pdf": compilar_latex,
        "png": convertir_pdf_png,
        "svg": convertir_pdf_svg,
        "eps": convertir_pdf_eps
    }
    
    for formato, funcion in formatos.items():
        try:
            archivo_salida = funcion(archivo_tikz, formato)
            print(f"✅ Exportado a {formato}: {archivo_salida}")
        except Exception as e:
            print(f"❌ Error exportando a {formato}: {e}")

def compilar_latex(archivo_tikz, formato):
    """Compilar TikZ a PDF."""
    subprocess.run(["pdflatex", archivo_tikz])
    return archivo_tikz.replace(".tikz", ".pdf")

def convertir_pdf_png(archivo_tikz, formato):
    """Convertir PDF a PNG de alta calidad."""
    pdf_file = archivo_tikz.replace(".tikz", ".pdf")
    png_file = archivo_tikz.replace(".tikz", ".png")
    subprocess.run(["convert", "-density", "300", pdf_file, png_file])
    return png_file
```

---

## 📊 **Métricas y Análisis Personalizados**

### **Sistema de Métricas Personalizado**

#### **Definir métricas específicas:**
```python
# En 02-Codigo-Agente/03-metricas_personalizadas.py
class MetricasPersonalizadas:
    """Sistema de métricas adaptado a casos específicos."""
    
    def __init__(self, tipo_uso="general"):
        self.tipo_uso = tipo_uso
        self.metricas = self._configurar_metricas()
    
    def _configurar_metricas(self):
        """Configurar métricas según tipo de uso."""
        
        if self.tipo_uso == "educativo":
            return {
                "claridad_visual": self._evaluar_claridad,
                "simplicidad_codigo": self._evaluar_simplicidad,
                "colores_apropiados": self._evaluar_colores_educativos
            }
        elif self.tipo_uso == "academico":
            return {
                "precision_matematica": self._evaluar_precision,
                "completitud_elementos": self._evaluar_completitud,
                "calidad_codigo": self._evaluar_calidad_codigo
            }
        else:
            return self._metricas_generales()
    
    def evaluar_resultado(self, archivo_tikz, imagen_original):
        """Evaluar resultado según métricas personalizadas."""
        
        resultados = {}
        for nombre, metrica in self.metricas.items():
            resultados[nombre] = metrica(archivo_tikz, imagen_original)
        
        return resultados
```

### **Dashboard de Análisis**

#### **Generar reportes personalizados:**
```python
def generar_reporte_personalizado(resultados_lote, tipo_uso):
    """Generar reporte adaptado al tipo de uso."""
    
    if tipo_uso == "educativo":
        return generar_reporte_educativo(resultados_lote)
    elif tipo_uso == "academico":
        return generar_reporte_academico(resultados_lote)
    else:
        return generar_reporte_general(resultados_lote)

def generar_reporte_educativo(resultados):
    """Reporte enfocado en aspectos educativos."""
    
    reporte = {
        "resumen": {
            "imagenes_procesadas": len(resultados),
            "claridad_promedio": calcular_promedio(resultados, "claridad_visual"),
            "simplicidad_promedio": calcular_promedio(resultados, "simplicidad_codigo")
        },
        "recomendaciones": generar_recomendaciones_educativas(resultados),
        "imagenes_problematicas": identificar_problemas_educativos(resultados)
    }
    
    return reporte
```

---

## 🔧 **Configuración de Desarrollo**

### **Entorno de Desarrollo Personalizado**

#### **Configuración para desarrollo:**
```json
{
  "configuracion_desarrollo": {
    "modo_debug": true,
    "guardar_archivos_temporales": true,
    "mostrar_progreso_detallado": true,
    "testing_automatico": true,
    "benchmark_rendimiento": true,
    "logs_verbosos": true
  },
  
  "configuracion_testing": {
    "ejecutar_tests_automaticos": true,
    "comparar_con_baseline": true,
    "generar_metricas_detalladas": true,
    "guardar_casos_fallidos": true
  }
}
```

#### **Scripts de desarrollo:**
```bash
# En scripts/desarrollo.sh
#!/bin/bash

# Configurar entorno de desarrollo
export AGENTE_DEBUG=1
export AGENTE_VERBOSE=1

# Ejecutar con configuración de desarrollo
python3 02-Codigo-Agente/01-agente_principal.py \
  --config desarrollo \
  --debug \
  --save-temps \
  "$@"
```

---

## 🎯 **Casos de Uso Específicos**

### **Personalización para Instituciones**

#### **Configuración institucional:**
```json
{
  "configuracion_institucion": {
    "nombre": "Universidad XYZ",
    "colores_institucionales": ["#003366", "#FF6600"],
    "logo_path": "assets/logo_universidad.png",
    "plantilla_documento": "templates/plantilla_universidad.tex",
    "estandares_graficos": {
      "fuente_minima": "10pt",
      "grosor_minimo": "thick",
      "resolucion_minima": "300dpi"
    }
  }
}
```

### **Personalización para Proyectos**

#### **Configuración de proyecto:**
```json
{
  "configuracion_proyecto": {
    "nombre": "Proyecto Matemáticas ICFES",
    "directorio_base": "/ruta/al/proyecto",
    "templates_personalizados": "templates/icfes/",
    "configuracion_especifica": {
      "enfoque_pedagogico": true,
      "nivel_educativo": "bachillerato",
      "estandares_icfes": true
    }
  }
}
```

---

## 🎉 **¡Personalización Completa Dominada!**

**Has aprendido a personalizar completamente el Agente TikZ para cualquier caso de uso específico.**

### **Habilidades adquiridas:**
- ✅ **Configuración profunda** del agente para casos específicos
- ✅ **Prompts especializados** por materia y contexto
- ✅ **Esquemas de colores** y estilos personalizados
- ✅ **Flujos de trabajo** automatizados y optimizados
- ✅ **Métricas personalizadas** para evaluación específica
- ✅ **Configuración institucional** y de proyecto

### **Beneficios obtenidos:**
- 🎯 **Adaptación perfecta** a tus necesidades específicas
- ⚡ **Automatización completa** de flujos repetitivos
- 📊 **Métricas relevantes** para tu contexto
- 🎨 **Consistencia visual** con estándares institucionales
- 🔧 **Control total** sobre el comportamiento del agente

### **Siguiente paso:** 📖 **`09-MEJORAS_FUTURAS.md`**

**¡Ahora el Agente TikZ está completamente adaptado a tu flujo de trabajo!** ⚙️✨

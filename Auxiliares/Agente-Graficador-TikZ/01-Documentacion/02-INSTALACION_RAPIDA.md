# ⚡ Instalación Rápida del Agente TikZ

## 🎯 **Instalación en 5 Minutos**

### **📋 Prerequisitos**
- ✅ **Python 3.8+** instalado
- ✅ **VSCode** instalado
- ✅ **LaTeX** instalado (TeX Live o MiKTeX)
- ⚠️ **QTikz/KTikz** (opcional, para testing visual)

---

## 🚀 **Paso 1: Verificar Sistema (1 minuto)**

### **Verificar Python:**
```bash
python3 --version
# Debe mostrar: Python 3.8.x o superior
```

### **Verificar VSCode:**
```bash
code --version
# Debe mostrar versión de VSCode
```

### **Verificar LaTeX:**
```bash
pdflatex --version
# Debe mostrar versión de LaTeX
```

### **Si falta algún prerequisito:**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip texlive-latex-extra code

# macOS (con Homebrew)
brew install python
brew install --cask visual-studio-code
brew install mactex

# Windows
# Descargar e instalar manualmente:
# - Python: python.org
# - VSCode: code.visualstudio.com
# - LaTeX: miktex.org
```

---

## 🔧 **Paso 2: Instalar Dependencias Python (2 minutos)**

### **Navegar al agente:**
```bash
cd Auxiliares/Agente-Graficador-TikZ/02-Codigo-Agente/
```

### **Instalar dependencias:**
```bash
pip install -r 06-requirements.txt
```

### **Verificar instalación:**
```bash
python3 -c "import cv2, numpy, PIL; print('✅ Dependencias OK')"
```

---

## ⚙️ **Paso 3: Configurar VSCode (1 minuto)**

### **Ejecutar configuración automática:**
```bash
cd ../03-Configuracion-VSCode/
python3 05-setup_vscode.py
```

### **Resultado esperado:**
```
🔧 CONFIGURANDO VSCODE PARA AGENTE TIKZ
==================================================
📁 Directorio del proyecto: /ruta/al/proyecto
📂 Directorio VSCode: /ruta/al/proyecto/.vscode
📋 Copiando archivos de configuración:
  ✅ tasks.json
  ✅ tikz.code-snippets
  ✅ settings.json
  ✅ keybindings.json
📁 Creando directorios:
  ✅ tikz_generado/
  ✅ logs/
🔍 Verificando dependencias:
  ✅ python3
  ✅ pdflatex
  ✅ convert
✅ CONFIGURACIÓN COMPLETADA
🚀 VSCode está listo para usar el Agente TikZ
```

---

## 🧪 **Paso 4: Probar Funcionamiento (1 minuto)**

### **Ejecutar tests automatizados:**
```bash
cd ../04-Ejemplos-y-Pruebas/
python3 05-test_agente.py
```

### **Resultado esperado:**
```
🧪 SUITE DE TESTS - AGENTE TIKZ + AUGMENT
==================================================
🧪 TEST 1: Configuración Básica
✅ Python 3.8+ disponible
✅ cv2 disponible
✅ numpy disponible
✅ PIL disponible
✅ ../02-Codigo-Agente/01-agente_principal.py existe
✅ ../03-Configuracion-VSCode/01-tasks.json existe
✅ LaTeX disponible
⚠️ QTikz (opcional) no disponible (opcional)
✅ TEST 1 EXITOSO: Configuración básica OK

🧪 TEST 2: Procesamiento de Imagen
🔄 Ejecutando agente...
✅ Agente ejecutado exitosamente
✅ 01-imagen_ejemplo_qtikz.tikz generado
✅ 01-imagen_ejemplo_agente.tikz generado
✅ 01-imagen_ejemplo_analisis.json generado
✅ TEST 2 EXITOSO: Procesamiento completado

📊 RESUMEN DE TESTS
==================================================
Test 1: ✅ EXITOSO
Test 2: ✅ EXITOSO
Test 3: ✅ EXITOSO
Test 4: ✅ EXITOSO
Test 5: ✅ EXITOSO

Resultado: 5/5 tests exitosos
🎉 ¡TODOS LOS TESTS EXITOSOS!
✅ El Agente TikZ está completamente funcional
```

---

## 🎨 **Paso 5: Primera Prueba Real (30 segundos)**

### **Abrir VSCode en el proyecto:**
```bash
cd ../../..
code .
```

### **Procesar imagen de ejemplo:**
```
1. En VSCode: File → Open → 04-Ejemplos-y-Pruebas/01-imagen_ejemplo.png
2. Presionar: Ctrl+Shift+P
3. Escribir: "Tasks: Run Task"
4. Seleccionar: "🎨 Agente TikZ + Augment: Analizar Imagen"
5. Esperar 30 segundos
6. Ver resultado en: tikz_generado/
```

### **Verificar resultado en QTikz (opcional):**
```bash
qtikz tikz_generado/01-imagen_ejemplo_qtikz.tikz
```

---

## ✅ **Verificación de Instalación Exitosa**

### **Archivos que deben existir:**
```
✅ .vscode/tasks.json                    # Tasks configurados
✅ .vscode/tikz.code-snippets           # Snippets TikZ
✅ tikz_generado/                       # Directorio de salida
✅ logs/                                # Directorio de logs
```

### **Funcionalidades que deben funcionar:**
```
✅ Tasks de VSCode aparecen en Command Palette
✅ Agente procesa imágenes sin errores
✅ Se generan archivos TikZ válidos
✅ QTikz abre archivos *_qtikz.tikz sin errores
✅ LaTeX compila archivos *_agente.tikz correctamente
```

---

## 🆘 **Solución de Problemas Comunes**

### **❌ "python3: command not found"**
```bash
# Verificar instalación Python
which python3
python --version

# Si no está instalado:
# Ubuntu/Debian: sudo apt install python3
# macOS: brew install python
# Windows: Descargar de python.org
```

### **❌ "pip install falla"**
```bash
# Actualizar pip
python3 -m pip install --upgrade pip

# Instalar con usuario
pip install --user -r 06-requirements.txt

# Si persiste, instalar individualmente:
pip install opencv-python numpy Pillow
```

### **❌ "Tasks no aparecen en VSCode"**
```bash
# Verificar archivo tasks.json
ls .vscode/tasks.json

# Recargar VSCode
# Ctrl+Shift+P → "Developer: Reload Window"

# Reconfigurar si es necesario
cd Auxiliares/Agente-Graficador-TikZ/03-Configuracion-VSCode/
python3 05-setup_vscode.py
```

### **❌ "pdflatex not found"**
```bash
# Ubuntu/Debian
sudo apt install texlive-latex-extra texlive-pictures

# macOS
brew install mactex

# Windows
# Descargar MiKTeX de miktex.org
```

### **❌ "QTikz no abre archivos"**
```
Solución: Usar archivo *_qtikz.tikz (no *_agente.tikz)
Los archivos *_qtikz.tikz son específicos para QTikz
Los archivos *_agente.tikz son para LaTeX completo
```

---

## 🎯 **Próximos Pasos**

### **Después de la instalación exitosa:**
```
1. 📖 Leer: 03-TUTORIAL_BASICO.md (procesar tu primera imagen)
2. 📖 Revisar: 05-REFERENCIA_VSCODE.md (flujo de trabajo diario)
3. 📖 Explorar: 07-TEMPLATES_GUIA.md (personalizar templates)
4. 🎨 Practicar: Procesar tus propias imágenes matemáticas
```

### **Para uso avanzado:**
```
1. 📖 Estudiar: 04-TUTORIAL_AVANZADO.md
2. 📖 Personalizar: 08-PERSONALIZACION.md
3. 📖 Contribuir: 09-MEJORAS_FUTURAS.md
```

---

## 🎉 **¡Instalación Completada!**

**El Agente Graficador TikZ + Augment + VSCode está listo para convertir tus imágenes matemáticas en código TikZ profesional.**

### **Tiempo total de instalación:** ⏱️ **5 minutos**
### **Estado:** ✅ **Completamente funcional**
### **Próximo paso:** 📖 **`03-TUTORIAL_BASICO.md`**

**¡Empieza a crear código TikZ profesional desde tus imágenes!** 🎨✨

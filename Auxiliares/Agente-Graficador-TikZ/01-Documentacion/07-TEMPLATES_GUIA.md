# 🎨 Guía Completa de Templates TikZ

## 🎯 **Dominar y Personalizar Templates del Agente TikZ**

Esta guía te enseña a usar, modificar y crear templates TikZ profesionales para casos específicos.

---

## 📁 **Templates Incluidos**

### **📊 01-template_funciones.tikz**
```
Uso: Funciones matemáticas (lineales, cuadráticas, trigonométricas)
Elementos: Ejes, cuadrí<PERSON>, función principal, puntos importantes
Personalizable: Tipo de función, dominio, rango, colores
```

### **📐 02-template_geometria.tikz**
```
Uso: Figuras geométricas con medidas y ángulos
Elementos: Vértices, lados, ángulos, medidas, construcciones
Personalizable: Tipo de figura, dimensiones, etiquetas
```

### **📈 03-template_diagramas.tikz**
```
Uso: Diagramas de flujo, mapas conceptuales, esquemas
Elementos: Nodos, conexiones, flechas, agrupaciones
Personalizable: Tipos de nodos, estilos de conexión, colores
```

### **📏 04-template_ejes.tikz**
```
Uso: Sistemas de coordenadas básicos
Elementos: Ejes X/Y, marcas, cuadrícula, origen
Personalizable: Rangos, escalas, estilos de cuadrícula
```

---

## 🔧 **Anatomía de un Template**

### **Estructura Estándar**

#### **Encabezado informativo:**
```latex
% ==============================
% TEMPLATE [TIPO]
% ==============================
% Agente Graficador TikZ + Augment
% Versión: 1.0.0
% Uso: [Descripción específica]
```

#### **Configuración inicial:**
```latex
\usetikzlibrary{calc, arrows.meta, positioning}
\begin{tikzpicture}[scale=1.2]

% Definir parámetros personalizables
\def\xmin{-4}
\def\xmax{4}
\def\ymin{-3}
\def\ymax{3}
```

#### **Elementos base:**
```latex
% Cuadrícula de fondo
\draw[gray!20, thin] (\xmin,\ymin) grid[step=0.5] (\xmax,\ymax);

% Ejes principales
\draw[thick, ->] (\xmin,0) -- (\xmax,0) node[below right] {$x$};
\draw[thick, ->] (0,\ymin) -- (0,\ymax) node[above left] {$y$};
```

#### **Contenido específico:**
```latex
% ELEMENTO PRINCIPAL - Personalizar según tipo
% [Código específico del template]
```

#### **Notas de uso:**
```latex
% ==============================
% NOTAS DE USO
% ==============================
% 1. Modificar parámetros según necesidad
% 2. Personalizar colores y estilos
% 3. Agregar/quitar elementos según contexto
```

---

## 🎨 **Personalizar Templates Existentes**

### **Template de Funciones - Casos Específicos**

#### **Función Cuadrática:**
```latex
% Modificar en 01-template_funciones.tikz
% Función cuadrática: y = ax² + bx + c
\def\a{0.5}
\def\b{0}
\def\c{-1}

\draw[blue, very thick] plot[domain=-3:3, samples=50] 
  (\x, {\a*\x^2 + \b*\x + \c});

% Vértice
\def\verticeX{-\b/(2*\a)}
\def\verticeY{\a*\verticeX^2 + \b*\verticeX + \c}
\fill[red] (\verticeX,\verticeY) circle (3pt) 
  node[below right] {Vértice $(\verticeX,\verticeY)$};
```

#### **Función Trigonométrica:**
```latex
% Función seno: y = A*sin(B*x + C) + D
\def\amplitud{2}
\def\frecuencia{1}
\def\fase{0}
\def\desplazamiento{0}

\draw[blue, very thick] plot[domain=-2*pi:2*pi, samples=100] 
  (\x, {\amplitud*sin(deg(\frecuencia*\x + \fase)) + \desplazamiento});

% Líneas de amplitud
\draw[dashed, red] (-2*pi,\amplitud) -- (2*pi,\amplitud) 
  node[right] {$A = \amplitud$};
\draw[dashed, red] (-2*pi,-\amplitud) -- (2*pi,-\amplitud) 
  node[right] {$-A = -\amplitud$};

% Marcas en múltiplos de π
\foreach \x in {-2,-1,1,2} {
  \draw (\x*pi,-0.1) -- (\x*pi,0.1) node[below] {$\x\pi$};
}
```

### **Template de Geometría - Casos Específicos**

#### **Triángulo con Circuncentro:**
```latex
% Modificar en 02-template_geometria.tikz
% Triángulo con circuncentro y circunradio

% Vértices del triángulo
\coordinate (A) at (0,0);
\coordinate (B) at (4,0);
\coordinate (C) at (2,3);

% Calcular circuncentro (simplificado)
\coordinate (O) at (2,1.17);

% Triángulo
\draw[blue, thick] (A) -- (B) -- (C) -- cycle;

% Circunferencia circunscrita
\draw[green, dashed] (O) circle (2.24);

% Marcar circuncentro
\fill[green] (O) circle (2pt) node[above right] {$O$ (Circuncentro)};

% Radios al circuncentro
\draw[green, thin] (O) -- (A);
\draw[green, thin] (O) -- (B);
\draw[green, thin] (O) -- (C);
```

#### **Cuadrado con Diagonales:**
```latex
% Cuadrado con diagonales y centro
\def\lado{3}

% Vértices
\coordinate (A) at (0,0);
\coordinate (B) at (\lado,0);
\coordinate (C) at (\lado,\lado);
\coordinate (D) at (0,\lado);
\coordinate (Centro) at (\lado/2,\lado/2);

% Cuadrado
\draw[blue, thick, fill=blue!10] (A) -- (B) -- (C) -- (D) -- cycle;

% Diagonales
\draw[red, dashed] (A) -- (C);
\draw[red, dashed] (B) -- (D);

% Centro
\fill[red] (Centro) circle (2pt) node[above right] {Centro};

% Medidas
\draw[|<->|] ($(A) + (0,-0.3)$) -- ($(B) + (0,-0.3)$) 
  node[midway, below] {$\lado$ cm};
```

---

## 🚀 **Crear Templates Personalizados**

### **Template para Funciones Exponenciales**

#### **Crear nuevo archivo:**
```bash
# En 05-Templates-TikZ/
touch 05-template_exponencial.tikz
```

#### **Contenido del template:**
```latex
% ==============================
% TEMPLATE FUNCIONES EXPONENCIALES
% ==============================
% Agente Graficador TikZ + Augment
% Versión: 1.0.0
% Uso: Funciones exponenciales y logarítmicas

\begin{tikzpicture}[scale=1.2]

% Parámetros personalizables
\def\base{2}           % Base de la exponencial
\def\factor{1}         % Factor multiplicativo
\def\desplazamiento{0} % Desplazamiento vertical

% Cuadrícula especializada para exponenciales
\draw[gray!15, thin] (-3,-1) grid[step=0.5] (3,5);

% Ejes con escala apropiada
\draw[thick, ->] (-3,0) -- (3,0) node[below right] {$x$};
\draw[thick, ->] (0,-1) -- (0,5) node[above left] {$y$};

% Función exponencial: y = a * b^x + c
\draw[blue, very thick] plot[domain=-2.5:2, samples=50] 
  (\x, {\factor*(\base)^(\x) + \desplazamiento});

% Asíntota horizontal
\draw[dashed, red] (-3,\desplazamiento) -- (3,\desplazamiento) 
  node[right] {$y = \desplazamiento$ (asíntota)};

% Punto especial (0, a+c)
\fill[red] (0,{\factor + \desplazamiento}) circle (3pt) 
  node[above right] {$(0, \factor + \desplazamiento)$};

% Punto especial (1, ab+c)
\fill[red] (1,{\factor*\base + \desplazamiento}) circle (3pt) 
  node[above right] {$(1, \factor \cdot \base + \desplazamiento)$};

% Marcas en ejes
\foreach \x in {-2,-1,1,2} {
  \draw (\x,-0.1) -- (\x,0.1) node[below] {\x};
}
\foreach \y in {1,2,3,4} {
  \draw (-0.1,\y) -- (0.1,\y) node[left] {\y};
}

% Ecuación de la función
\node[above left] at (-2,4) {$y = \factor \cdot \base^x + \desplazamiento$};

% Propiedades
\node[below right] at (1,1) {
  \begin{tabular}{l}
    Base: $\base$ \\
    Factor: $\factor$ \\
    Asíntota: $y = \desplazamiento$
  \end{tabular}
};

\end{tikzpicture}

% ==============================
% NOTAS DE USO
% ==============================
% 1. Cambiar \base para diferentes bases (e, 2, 10, etc.)
% 2. Ajustar \factor para escalamiento vertical
% 3. Modificar \desplazamiento para mover asíntota
% 4. Para función logarítmica, intercambiar x e y en plot
% 5. Ajustar dominio según comportamiento de la función

% EJEMPLOS DE CONFIGURACIÓN:
% Exponencial natural: \def\base{2.718}
% Exponencial base 10: \def\base{10}
% Decaimiento: \def\factor{-1}
% Crecimiento rápido: \def\base{3}
```

### **Template para Estadística**

#### **Crear template de histogramas:**
```latex
% En 05-Templates-TikZ/06-template_estadistica.tikz
% ==============================
% TEMPLATE ESTADÍSTICA
% ==============================

\usetikzlibrary{patterns}
\begin{tikzpicture}[scale=1.2]

% Datos del histograma (personalizar)
\def\datos{{1,3,5,7,4,2,1}} % Frecuencias
\def\etiquetas{{"0-10","10-20","20-30","30-40","40-50","50-60","60-70"}}

% Configuración
\def\anchobarra{0.8}
\def\maxaltura{8}

% Ejes
\draw[thick, ->] (0,0) -- (8,0) node[below right] {Intervalos};
\draw[thick, ->] (0,0) -- (0,\maxaltura) node[above left] {Frecuencia};

% Barras del histograma
\foreach \i in {0,1,2,3,4,5,6} {
  \pgfmathsetmacro{\altura}{\datos[\i]}
  \pgfmathsetmacro{\etiqueta}{\etiquetas[\i]}
  
  % Barra
  \draw[blue, thick, fill=blue!30] 
    (\i + 0.1, 0) rectangle (\i + 0.9, \altura);
  
  % Etiqueta en X
  \node[below, rotate=45] at (\i + 0.5, -0.2) {\etiqueta};
  
  % Valor de frecuencia
  \node[above] at (\i + 0.5, \altura + 0.1) {\altura};
}

% Marcas en Y
\foreach \y in {1,2,...,7} {
  \draw (-0.1,\y) -- (0.1,\y) node[left] {\y};
}

% Título y estadísticas
\node[above] at (3.5, \maxaltura - 0.5) {\textbf{Histograma de Frecuencias}};

% Estadísticas básicas
\node[right] at (8.5, 6) {
  \begin{tabular}{l}
    $n = 23$ \\
    $\bar{x} = 32.1$ \\
    $s = 15.7$ \\
    Mediana $= 30$
  \end{tabular}
};

\end{tikzpicture}
```

---

## 🔧 **Configurar Templates en el Agente**

### **Registrar Nuevos Templates**

#### **Editar configuración:**
```json
// En 02-Codigo-Agente/05-configuracion.json
{
  "configuracion_templates": {
    "template_funciones": "05-Templates-TikZ/01-template_funciones.tikz",
    "template_geometria": "05-Templates-TikZ/02-template_geometria.tikz",
    "template_diagramas": "05-Templates-TikZ/03-template_diagramas.tikz",
    "template_ejes": "05-Templates-TikZ/04-template_ejes.tikz",
    "template_exponencial": "05-Templates-TikZ/05-template_exponencial.tikz",
    "template_estadistica": "05-Templates-TikZ/06-template_estadistica.tikz"
  },
  "mapeo_tipo_template": {
    "funcion_exponencial": "template_exponencial",
    "funcion_logaritmica": "template_exponencial",
    "histograma": "template_estadistica",
    "diagrama_barras": "template_estadistica"
  }
}
```

### **Usar Templates Específicos**

#### **Con prompts dirigidos:**
```
"Analiza esta función exponencial. Usa template especializado para exponenciales con base e, identifica asíntota horizontal y puntos clave."
```

#### **Modificar código del agente:**
```python
# En 02-Codigo-Agente/01-agente_principal.py
def seleccionar_template(tipo_grafica, elementos_detectados):
    """Seleccionar template más apropiado."""
    
    if "exponencial" in tipo_grafica.lower():
        return "template_exponencial"
    elif "estadistica" in tipo_grafica.lower() or "histograma" in elementos_detectados:
        return "template_estadistica"
    elif "funcion" in tipo_grafica.lower():
        return "template_funciones"
    # ... más lógica de selección
```

---

## 🎨 **Templates Avanzados**

### **Template con Animación (para Beamer)**

#### **Template animado:**
```latex
% En 05-Templates-TikZ/07-template_animado.tikz
\begin{tikzpicture}[scale=1.2]

% Frame 1: Solo ejes
\only<1>{
  \draw[thick, ->] (-3,0) -- (3,0) node[below right] {$x$};
  \draw[thick, ->] (0,-2) -- (0,2) node[above left] {$y$};
}

% Frame 2: Agregar cuadrícula
\only<2->{
  \draw[gray!20, thin] (-3,-2) grid[step=0.5] (3,2);
  \draw[thick, ->] (-3,0) -- (3,0) node[below right] {$x$};
  \draw[thick, ->] (0,-2) -- (0,2) node[above left] {$y$};
}

% Frame 3: Agregar función
\only<3->{
  \draw[blue, very thick] plot[domain=-2.5:2.5, samples=50] 
    (\x, {\x^2 - 1});
}

% Frame 4: Agregar puntos importantes
\only<4->{
  \fill[red] (0,-1) circle (3pt) node[below right] {Vértice};
  \fill[blue] (-1,0) circle (2pt);
  \fill[blue] (1,0) circle (2pt);
}

\end{tikzpicture}
```

### **Template 3D Básico**

#### **Figuras tridimensionales:**
```latex
% En 05-Templates-TikZ/08-template_3d.tikz
\usetikzlibrary{3d}
\begin{tikzpicture}[x={(1cm,0cm)}, y={(0.5cm,0.5cm)}, z={(0cm,1cm)}]

% Ejes 3D
\draw[thick, ->] (0,0,0) -- (4,0,0) node[right] {$x$};
\draw[thick, ->] (0,0,0) -- (0,4,0) node[above] {$y$};
\draw[thick, ->] (0,0,0) -- (0,0,4) node[above] {$z$};

% Cubo 3D
\draw[blue, thick] 
  (0,0,0) -- (2,0,0) -- (2,2,0) -- (0,2,0) -- cycle
  (0,0,2) -- (2,0,2) -- (2,2,2) -- (0,2,2) -- cycle
  (0,0,0) -- (0,0,2)
  (2,0,0) -- (2,0,2)
  (2,2,0) -- (2,2,2)
  (0,2,0) -- (0,2,2);

% Superficie paramétrica (ejemplo)
\foreach \u in {0,0.2,...,2} {
  \foreach \v in {0,0.2,...,2} {
    \fill[red, opacity=0.3] 
      (\u,\v,{\u*\v/2}) -- (\u+0.2,\v,{(\u+0.2)*\v/2}) -- 
      (\u+0.2,\v+0.2,{(\u+0.2)*(\v+0.2)/2}) -- (\u,\v+0.2,{\u*(\v+0.2)/2}) -- cycle;
  }
}

\end{tikzpicture}
```

---

## 📊 **Biblioteca de Snippets para Templates**

### **Elementos Comunes Reutilizables**

#### **Cuadrícula personalizable:**
```latex
% Snippet: cuadricula-personalizada
\def\cuadricula#1#2#3#4#5{
  % #1=xmin, #2=xmax, #3=ymin, #4=ymax, #5=step
  \draw[gray!20, thin] (#1,#3) grid[step=#5] (#2,#4);
}

% Uso: \cuadricula{-4}{4}{-3}{3}{0.5}
```

#### **Ejes con marcas automáticas:**
```latex
% Snippet: ejes-automaticos
\def\ejes#1#2#3#4{
  % #1=xmin, #2=xmax, #3=ymin, #4=ymax
  \draw[thick, ->] (#1,0) -- (#2,0) node[below right] {$x$};
  \draw[thick, ->] (0,#3) -- (0,#4) node[above left] {$y$};
  
  \foreach \x in {#1,...,-1,1,2,...,#2} {
    \ifnum\x=0\else
      \draw (\x,-0.1) -- (\x,0.1) node[below] {\x};
    \fi
  }
  
  \foreach \y in {#3,...,-1,1,2,...,#4} {
    \ifnum\y=0\else
      \draw (-0.1,\y) -- (0.1,\y) node[left] {\y};
    \fi
  }
}
```

#### **Función genérica:**
```latex
% Snippet: funcion-generica
\def\funcion#1#2#3#4#5{
  % #1=expresión, #2=dominio_min, #3=dominio_max, #4=samples, #5=color
  \draw[#5, very thick] plot[domain=#2:#3, samples=#4] (\x, {#1});
}

% Uso: \funcion{\x^2 - 1}{-3}{3}{50}{blue}
```

---

## 🎯 **Mejores Prácticas para Templates**

### **Diseño Modular**

#### **Separar en secciones:**
```latex
% 1. CONFIGURACIÓN
\def\parametros{...}

% 2. ELEMENTOS BASE
\draw cuadrícula y ejes...

% 3. CONTENIDO PRINCIPAL
\draw elemento específico...

% 4. ANOTACIONES
\node etiquetas y medidas...

% 5. DECORACIONES
\draw elementos adicionales...
```

#### **Usar estilos consistentes:**
```latex
% Definir estilos al inicio
\tikzset{
  eje/.style={thick, ->},
  cuadricula/.style={gray!20, thin},
  funcion/.style={blue, very thick},
  punto/.style={red, fill, circle, inner sep=2pt},
  etiqueta/.style={black, font=\small}
}
```

### **Documentación Interna**

#### **Comentarios descriptivos:**
```latex
% ==============================
% SECCIÓN: CONFIGURACIÓN
% ==============================
% Parámetros que el usuario debe modificar

% ==============================
% SECCIÓN: ELEMENTOS BASE
% ==============================
% Elementos que aparecen en todos los casos

% ==============================
% SECCIÓN: PERSONALIZACIÓN
% ==============================
% Código específico que varía según caso
```

#### **Ejemplos de uso:**
```latex
% ==============================
% EJEMPLOS DE USO
% ==============================
% Para parábola: \def\funcion{\x^2}
% Para seno: \def\funcion{sin(deg(\x))}
% Para exponencial: \def\funcion{exp(\x)}
```

---

## 🎉 **¡Maestría en Templates Completada!**

**Has dominado completamente el sistema de templates del Agente TikZ.**

### **Habilidades adquiridas:**
- ✅ **Uso experto** de templates incluidos
- ✅ **Personalización avanzada** para casos específicos
- ✅ **Creación de templates** completamente nuevos
- ✅ **Configuración del agente** para usar templates personalizados
- ✅ **Biblioteca de snippets** reutilizables

### **Beneficios obtenidos:**
- 🎨 **Código TikZ consistente** y profesional
- ⚡ **Desarrollo rápido** con templates especializados
- 🔧 **Flexibilidad total** para casos únicos
- 📚 **Biblioteca personal** de templates

### **Siguiente paso:** 📖 **`08-PERSONALIZACION.md`**

**¡Ahora puedes crear templates TikZ profesionales para cualquier caso de uso!** 🎨✨

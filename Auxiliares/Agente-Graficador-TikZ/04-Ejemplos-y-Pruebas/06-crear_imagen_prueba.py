#!/usr/bin/env python3
"""
🖼️ Crear imagen de prueba para el Agente TikZ
==============================================

Script para crear una imagen matemática simple para probar el agente.
"""

def crear_imagen_simple():
    """Crear imagen matemática simple usando caracteres ASCII."""
    
    # Crear imagen ASCII de una función cuadrática
    imagen_ascii = """
    Y
    |
  4 +     *
    |   *   *
  3 +  *     *
    | *       *
  2 + *       *
    |*         *
  1 +           *
    |             *
  0 +---+---+---+---+---+----> X
    0   1   2   3   4   5   6
    
    Función: y = -(x-3)² + 4
    """
    
    # Guardar como archivo de texto
    with open("imagenes/funcion_cuadratica_ascii.txt", 'w', encoding='utf-8') as f:
        f.write(imagen_ascii)
    
    print("✅ Imagen ASCII creada: imagenes/funcion_cuadratica_ascii.txt")
    
    # Crear descripción de la imagen
    descripcion = """
# 📊 Descripción de la Imagen de Prueba

## Tipo: Función Matemática
- **Función:** y = -(x-3)² + 4
- **Tipo:** Parábola invertida
- **Vértice:** (3, 4)
- **Ejes:** X de 0 a 6, Y de 0 a 4
- **Puntos importantes:** 
  - Vértice en (3, 4)
  - Intersecciones con eje X aproximadamente en (1, 0) y (5, 0)
  - Intersección con eje Y en (0, -5) [fuera del rango mostrado]

## Elementos Visuales
- **Ejes coordenados:** Claramente marcados
- **Cuadrícula:** Implícita con marcas cada unidad
- **Curva:** Representada con asteriscos (*)
- **Escala:** Uniforme en ambos ejes

## Para el Agente TikZ
Esta imagen debería generar código TikZ que incluya:
1. Ejes coordenados con flechas
2. Cuadrícula de fondo
3. Parábola invertida con vértice en (3, 4)
4. Etiquetas en los ejes
5. Marcas de escala
"""
    
    with open("imagenes/descripcion_prueba.md", 'w', encoding='utf-8') as f:
        f.write(descripcion)
    
    print("✅ Descripción creada: imagenes/descripcion_prueba.md")

def crear_ejemplo_uso():
    """Crear ejemplo de uso del agente."""
    
    ejemplo = """#!/usr/bin/env python3
# 🎯 Ejemplo de uso del Agente TikZ + Augment

# Importar el agente
from vscode_integration import AgenteTikZAugment

# Crear instancia del agente
agente = AgenteTikZAugment()

# Procesar imagen con prompt personalizado
resultado = agente.procesar_imagen_completo(
    ruta_imagen="imagenes/funcion_cuadratica_ascii.txt",
    prompt_personalizado="Analiza esta función cuadrática representada en ASCII. Identifica el vértice, la orientación de la parábola y genera código TikZ profesional.",
    guardar_automatico=True
)

# Mostrar resultado
if resultado['exitoso']:
    print("✅ Procesamiento exitoso!")
    print("📄 Código TikZ generado:")
    print("-" * 50)
    print(resultado['codigo_tikz'])
    print("-" * 50)
    
    if 'archivo_guardado' in resultado:
        print(f"💾 Archivo guardado: {resultado['archivo_guardado']}")
else:
    print(f"❌ Error: {resultado.get('error', 'Error desconocido')}")
"""
    
    with open("ejemplo_uso_agente.py", 'w', encoding='utf-8') as f:
        f.write(ejemplo)
    
    print("✅ Ejemplo de uso creado: ejemplo_uso_agente.py")

if __name__ == "__main__":
    print("🖼️ CREANDO IMAGEN DE PRUEBA PARA AGENTE TIKZ")
    print("=" * 50)
    
    crear_imagen_simple()
    crear_ejemplo_uso()
    
    print("\n🎯 ARCHIVOS CREADOS:")
    print("1. imagenes/funcion_cuadratica_ascii.txt - Imagen ASCII de prueba")
    print("2. imagenes/descripcion_prueba.md - Descripción detallada")
    print("3. ejemplo_uso_agente.py - Ejemplo de uso del agente")
    
    print("\n🚀 PRÓXIMO PASO:")
    print("Ejecutar: python3 ejemplo_uso_agente.py")
    print("O usar desde VSCode con Ctrl+Shift+P → Tasks: Run Task")

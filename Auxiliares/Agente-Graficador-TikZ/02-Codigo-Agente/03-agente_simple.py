#!/usr/bin/env python3
"""
🎨 Agente TikZ Simplificado para Pruebas
=======================================

Versión simplificada del agente para demostración sin dependencias complejas.
"""

import os
import sys
from pathlib import Path

class AgenteTikZSimple:
    """Agente simplificado para demostración."""
    
    def __init__(self):
        print("🎨 Agente TikZ Simplificado inicializado")
        self.templates = self._cargar_templates()
    
    def _cargar_templates(self):
        """Cargar templates disponibles."""
        templates = {}
        templates_dir = Path("templates")
        
        if templates_dir.exists():
            for archivo in templates_dir.glob("*.tikz"):
                with open(archivo, 'r', encoding='utf-8') as f:
                    templates[archivo.stem] = f.read()
                print(f"✅ Template cargado: {archivo.stem}")
        
        return templates
    
    def analizar_imagen_simulado(self, ruta_imagen, tipo_esperado="auto"):
        """Simular análisis de imagen."""
        print(f"🔍 Analizando imagen: {ruta_imagen}")
        
        # Verificar que la imagen existe
        if not Path(ruta_imagen).exists():
            return {
                'exitoso': False,
                'error': f'Imagen no encontrada: {ruta_imagen}'
            }
        
        # Simular detección de tipo basado en nombre de archivo
        nombre = Path(ruta_imagen).stem.lower()
        
        if 'funcion' in nombre or 'grafica' in nombre or 'plot' in nombre:
            tipo_detectado = 'funcion'
        elif 'triangulo' in nombre or 'circulo' in nombre or 'geometria' in nombre:
            tipo_detectado = 'geometria'
        elif 'diagrama' in nombre or 'flujo' in nombre:
            tipo_detectado = 'diagrama'
        else:
            tipo_detectado = 'general'
        
        print(f"🎯 Tipo detectado: {tipo_detectado}")
        
        # Simular análisis específico
        analisis = {
            'exitoso': True,
            'tipo_detectado': tipo_detectado,
            'ruta_imagen': ruta_imagen,
            'dimensiones': (800, 600),  # Simulado
        }
        
        if tipo_detectado == 'funcion':
            analisis.update({
                'ejes': {'detectados': True},
                'curvas': [{'tipo': 'cuadratica', 'puntos_estimados': 50}],
                'cuadricula': {'presente': True},
                'puntos_importantes': 2
            })
        elif tipo_detectado == 'geometria':
            analisis.update({
                'formas': [{'tipo': 'triangulo', 'vertices': 3}],
                'angulos': 3,
                'medidas': ['lado_a', 'lado_b', 'lado_c']
            })
        elif tipo_detectado == 'diagrama':
            analisis.update({
                'nodos': 5,
                'conexiones': 4,
                'direcciones': ['arriba', 'abajo']
            })
        
        return analisis
    
    def generar_tikz(self, analisis):
        """Generar código TikZ basado en análisis."""
        print("🛠️ Generando código TikZ...")
        
        tipo = analisis.get('tipo_detectado', 'general')
        
        # Usar template apropiado
        if tipo in self.templates:
            codigo_base = self.templates[tipo]
            print(f"📋 Usando template: {tipo}")
        else:
            # Template por defecto
            codigo_base = self._template_default()
            print("📋 Usando template por defecto")
        
        # Personalizar código basado en análisis
        codigo_personalizado = self._personalizar_codigo(codigo_base, analisis)
        
        return codigo_personalizado
    
    def _template_default(self):
        """Template por defecto."""
        return """\\begin{tikzpicture}[scale=1.2]
% Gráfica generada automáticamente
\\draw[thick, ->] (0,0) -- (4,0) node[below right] {$x$};
\\draw[thick, ->] (0,0) -- (0,3) node[above left] {$y$};
\\draw[blue, thick] (0.5,0.5) -- (3,2.5);
\\fill[black] (0,0) circle (1.5pt);
\\end{tikzpicture}"""
    
    def _personalizar_codigo(self, codigo, analisis):
        """Personalizar código basado en análisis."""
        tipo = analisis.get('tipo_detectado', 'general')
        
        # Agregar comentario específico
        comentario = f"% Tipo detectado: {tipo}\n% Imagen: {analisis.get('ruta_imagen', 'N/A')}\n"
        
        # Insertar comentario después de \\begin{tikzpicture}
        lineas = codigo.split('\n')
        for i, linea in enumerate(lineas):
            if '\\begin{tikzpicture}' in linea:
                lineas.insert(i + 1, comentario)
                break
        
        return '\n'.join(lineas)
    
    def procesar_imagen(self, ruta_imagen, descripcion="", tipo_esperado="auto"):
        """Procesar imagen completa."""
        print(f"\n🎯 PROCESANDO IMAGEN: {ruta_imagen}")
        print("=" * 50)
        
        # 1. Análisis
        analisis = self.analizar_imagen_simulado(ruta_imagen, tipo_esperado)
        
        if not analisis.get('exitoso', False):
            print(f"❌ Error en análisis: {analisis.get('error', 'Error desconocido')}")
            return None
        
        # 2. Generación
        codigo_tikz = self.generar_tikz(analisis)
        
        # 3. Resultado
        resultado = {
            'imagen_original': ruta_imagen,
            'analisis': analisis,
            'codigo_tikz': codigo_tikz,
            'exitoso': True
        }
        
        print("✅ Procesamiento completado")
        return resultado
    
    def mostrar_resultado(self, resultado):
        """Mostrar resultado de procesamiento."""
        if not resultado:
            print("❌ No hay resultado para mostrar")
            return
        
        print("\n📊 RESULTADO DEL PROCESAMIENTO")
        print("=" * 50)
        
        analisis = resultado['analisis']
        print(f"📁 Imagen: {resultado['imagen_original']}")
        print(f"🎯 Tipo detectado: {analisis['tipo_detectado']}")
        print(f"📐 Dimensiones: {analisis['dimensiones']}")
        
        # Mostrar características específicas
        if analisis['tipo_detectado'] == 'funcion':
            print(f"📈 Ejes detectados: {analisis.get('ejes', {}).get('detectados', False)}")
            print(f"📊 Curvas: {len(analisis.get('curvas', []))}")
            print(f"🔲 Cuadrícula: {analisis.get('cuadricula', {}).get('presente', False)}")
        
        print(f"\n📝 CÓDIGO TIKZ GENERADO:")
        print("-" * 30)
        print(resultado['codigo_tikz'])
        print("-" * 30)
    
    def guardar_resultado(self, resultado, archivo_salida=None):
        """Guardar código TikZ en archivo."""
        if not resultado:
            print("❌ No hay resultado para guardar")
            return
        
        if not archivo_salida:
            nombre_imagen = Path(resultado['imagen_original']).stem
            archivo_salida = f"ejemplos/salida/{nombre_imagen}.tikz"
        
        # Crear directorio si no existe
        Path(archivo_salida).parent.mkdir(parents=True, exist_ok=True)
        
        # Guardar código
        with open(archivo_salida, 'w', encoding='utf-8') as f:
            f.write(resultado['codigo_tikz'])
        
        print(f"💾 Código guardado en: {archivo_salida}")
        return archivo_salida

def demo_con_imagen():
    """Demo interactivo con imagen."""
    print("🎨 DEMO AGENTE TIKZ SIMPLIFICADO")
    print("=" * 40)
    
    agente = AgenteTikZSimple()
    
    print(f"\n📋 Templates disponibles: {list(agente.templates.keys())}")
    
    # Solicitar imagen
    print("\n📁 Ingresa la ruta de tu imagen:")
    print("Ejemplos:")
    print("  - ejemplos/entrada/mi_funcion.png")
    print("  - /ruta/completa/a/imagen.jpg")
    print("  - imagen_triangulo.png")
    
    try:
        ruta_imagen = input("\n🖼️ Ruta de imagen: ").strip()
        
        if not ruta_imagen:
            print("❌ No se proporcionó ruta de imagen")
            return
        
        # Procesar imagen
        resultado = agente.procesar_imagen(ruta_imagen)
        
        if resultado:
            # Mostrar resultado
            agente.mostrar_resultado(resultado)
            
            # Preguntar si guardar
            guardar = input("\n💾 ¿Guardar código TikZ? (s/n): ").strip().lower()
            if guardar in ['s', 'si', 'sí', 'y', 'yes']:
                archivo_guardado = agente.guardar_resultado(resultado)
                print(f"✅ Archivo guardado: {archivo_guardado}")
        
    except KeyboardInterrupt:
        print("\n👋 Demo cancelado por el usuario")
    except Exception as e:
        print(f"❌ Error en demo: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Modo con argumentos
        ruta_imagen = sys.argv[1]
        agente = AgenteTikZSimple()
        resultado = agente.procesar_imagen(ruta_imagen)
        if resultado:
            agente.mostrar_resultado(resultado)
            agente.guardar_resultado(resultado)
    else:
        # Modo interactivo
        demo_con_imagen()

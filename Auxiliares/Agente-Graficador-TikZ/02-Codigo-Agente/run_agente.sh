#!/bin/bash
# Script wrapper para ejecutar el Agente TikZ con entorno virtual
# Uso: ./run_agente.sh <imagen> [prompt_personalizado]

# Activar entorno virtual
source venv_tikz/bin/activate

# Verificar argumentos
if [ $# -eq 0 ]; then
    echo "❌ Error: Se requiere especificar una imagen"
    echo "📖 Uso: $0 <imagen> [prompt_personalizado]"
    exit 1
fi

# Ejecutar agente con argumentos
if [ $# -eq 1 ]; then
    python3 01-agente_principal_mejorado.py "$1"
else
    python3 01-agente_principal_mejorado.py "$1" "$2"
fi

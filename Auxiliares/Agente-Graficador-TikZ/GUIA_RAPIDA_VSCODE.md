# 🎨 Guía Rápida: Agente TikZ en VSCode

## ✅ Estado de Configuración
- **Dependencias Python**: ✅ Instaladas en entorno virtual
- **Tasks VSCode**: ✅ Configurados y funcionando
- **Agente TikZ**: ✅ Operativo en modo compatibilidad

## 🚀 Cómo Usar los Task Lists

### 1. Abrir VSCode desde el directorio correcto
```bash
cd "/home/<USER>/Insync/<EMAIL>/Google Drive/RepositorioMatematicasICFES_R_Exams"
code .
```

### 2. Acceder a los Tasks
- **Método 1**: `Ctrl+Shift+P` → "Tasks: Run Task"
- **Método 2**: `Terminal` → `Run Task...`
- **Método 3**: `F1` → "Tasks: Run Task"

### 3. Tasks Disponibles

#### 🎨 **Agente TikZ v2.0: <PERSON><PERSON><PERSON> (Mejorado)**
- **Uso**: Selecciona una imagen (.png, .jpg) y ejecuta
- **Función**: Análisis automático con detección de contexto
- **Salida**: Genera archivos TikZ optimizados

#### 🎨 **Agente TikZ v2.0: Análisis Personalizado**
- **Uso**: Selecciona imagen + prompt personalizado
- **Función**: Análisis con instrucciones específicas
- **Ejemplo prompt**: "Analiza este ejercicio ICFES sobre geometría"

#### 🎨 **Agente TikZ v1.0: Análisis Clásico**
- **Uso**: Versión de compatibilidad
- **Función**: Análisis básico sin mejoras v2.0

#### 📋 **Compilar TikZ con LaTeX**
- **Uso**: Selecciona archivo .tikz y compila
- **Función**: Genera PDF desde código TikZ

#### 🔍 **Ver Resultados TikZ Generados**
- **Uso**: Lista archivos en tikz_generado/
- **Función**: Verificar salidas del agente

#### 🧪 **Test Mejoras Agente v2.0**
- **Uso**: Ejecuta tests de verificación
- **Función**: Validar funcionamiento del sistema

#### 🎨 **Abrir QTikz con Resultado**
- **Uso**: Abre resultado en QTikz (si está instalado)
- **Función**: Visualización interactiva

## 📁 Estructura de Archivos Generados

```
tikz_generado/
├── imagen_agente.tikz      # Código TikZ para LaTeX
├── imagen_qtikz.tikz       # Código TikZ para QTikz
└── imagen_analisis.json    # Metadatos del análisis
```

## 🔧 Solución de Problemas

### Si los tasks no aparecen:
1. Verificar que VSCode esté abierto desde la raíz del repositorio
2. Comprobar que existe `.vscode/tasks.json`
3. Recargar VSCode: `Ctrl+Shift+P` → "Developer: Reload Window"

### Si hay errores de Python:
1. El entorno virtual se activa automáticamente
2. Las dependencias están instaladas en `venv_tikz/`
3. Usar el script wrapper: `./run_agente.sh imagen.png`

## 🎯 Flujo de Trabajo Recomendado

1. **Preparar imagen**: Coloca tu imagen en cualquier carpeta del proyecto
2. **Seleccionar imagen**: Abre la imagen en VSCode
3. **Ejecutar task**: `Ctrl+Shift+P` → "Tasks: Run Task" → "🎨 Agente TikZ v2.0: Analizar Imagen"
4. **Revisar resultados**: Usar task "🔍 Ver Resultados TikZ Generados"
5. **Compilar (opcional)**: Usar task "📋 Compilar TikZ con LaTeX"

## 📝 Notas Importantes

- El agente funciona en **modo compatibilidad** (algunos módulos especializados no están disponibles)
- Los resultados se guardan automáticamente en `tikz_generado/`
- El entorno virtual se activa automáticamente con cada task
- Compatible con imágenes PNG, JPG, JPEG
